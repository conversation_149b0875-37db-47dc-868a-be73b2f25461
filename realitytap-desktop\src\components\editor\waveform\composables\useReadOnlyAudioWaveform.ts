// 只读音频波形绘制组合式函数
// 专门用于只读显示音频波形，不包含交互功能
// 从 useAudioWaveform.ts 中提取核心绘制逻辑

import { computed, readonly, ref, type Ref } from "vue";
import { calculateOptimizedAmplitudeScale, type AmplitudeScaleConfig } from "../utils/audio-amplitude-helpers";
import { EVENT_WAVEFORM_VERTICAL_OFFSET } from "../config/waveform-constants";
// import { audioLogger } from "@/utils/logger/logger"; // 暂时未使用
import { useFileWaveformEditorStore } from "@/stores/haptics-editor-store";

// 音频振幅数据接口（复用现有定义）
export interface AudioAmplitudeData {
  samples: number[];
  sample_rate: number;
  duration_ms: number;
  max_amplitude: number;
  min_amplitude: number;
}

// 只读音频波形配置接口
export interface ReadOnlyAudioWaveformConfig {
  // 坐标转换函数
  mapIntensityToYLocal: (intensity: number) => number;

  // 画布尺寸获取函数
  getGraphAreaWidth: () => number;
  getGraphAreaHeight: () => number;
  getLogicalGraphAreaWidth: () => number;

  // 虚拟滚动偏移
  virtualScrollOffset: Ref<number>;

  // 当前缩放级别
  currentZoomLevel?: Ref<number>;

  // 音频波形显示选项
  enableSilenceFiltering?: boolean; // 是否启用无声区域过滤，默认 true
  silenceThresholdPercent?: number; // 无声阈值百分比，默认 2%
  showWaveformBorder?: boolean; // 是否显示波形边框，默认 false
  amplitudeScale?: number; // 振幅缩放比例，默认 0.75
  amplitudeBoost?: number; // 振幅增强倍数，默认 1.5

  // 可见时间范围（用于过滤绘制）
  visibleStartTime?: Ref<number>; // 可见开始时间（ms）
  visibleEndTime?: Ref<number>; // 可见结束时间（ms）
}

// 音频波形缓存项
interface AudioWaveformCacheItem {
  data: AudioAmplitudeData;
  renderedPath: Path2D;
  lastCanvasWidth: number;
  lastScrollOffset: number;
  lastZoomLevel?: number;
  lastVisibleStartTime?: number;
  lastVisibleEndTime?: number;
}

/**
 * 只读音频波形管理 Composable
 * 专门用于只读显示音频波形，不包含加载和交互功能
 */
export function useReadOnlyAudioWaveform(config: ReadOnlyAudioWaveformConfig, fileUuid: string) {
  const {
    mapIntensityToYLocal,
    getGraphAreaWidth,
    getGraphAreaHeight,
    getLogicalGraphAreaWidth,
    virtualScrollOffset,
    currentZoomLevel,
    enableSilenceFiltering = true,
    silenceThresholdPercent = 2,
    showWaveformBorder = false,
    amplitudeScale = 0.75,
    amplitudeBoost = 1.5,
    visibleStartTime,
    visibleEndTime,
  } = config;

  // 获取文件级别的 Store 实例
  const waveformStore = useFileWaveformEditorStore(fileUuid);

  // 音频波形缓存
  const waveformCache = ref<AudioWaveformCacheItem | null>(null);

  // 从 Store 获取音频数据
  const audioData = computed(() => {
    return waveformStore.audioAmplitudeData;
  });

  // 检查是否有音频数据
  const hasAudioData = computed(() => {
    const data = audioData.value;
    return data !== null && data.samples && Array.isArray(data.samples) && data.samples.length > 0;
  });

  // 计算音频时长（毫秒）
  const audioDurationMs = computed(() => {
    return audioData.value?.duration_ms || 0;
  });

  // 计算音频采样率
  const audioSampleRate = computed(() => {
    return audioData.value?.sample_rate || 44100;
  });

  /**
   * 获取振幅配置（根据显示模式动态配置）
   */
  const getAmplitudeConfig = () => {
    // 使用默认的标准模式配置
    const preset = {
      amplitudeScale: 0.75,
      amplitudeBoost: 1.5,
    };

    return {
      amplitudeScale: preset.amplitudeScale,
      amplitudeBoost: preset.amplitudeBoost,
    };
  };

  /**
   * 检查是否需要重新生成波形路径
   */
  const shouldRegenerateWaveform = (): boolean => {
    if (!waveformCache.value || !audioData.value) return true;

    const cache = waveformCache.value;
    const currentWidth = getGraphAreaWidth();
    const currentZoom = currentZoomLevel?.value || 1.0;

    // 检查数据是否变化
    const dataChanged = cache.data !== audioData.value;

    // 检查画布尺寸是否变化
    const sizeChanged = Math.abs(cache.lastCanvasWidth - currentWidth) > 1;

    // 注意：对于拉伸铺满模式，虚拟滚动偏移不影响绘制，所以不检查偏移变化
    const offsetChanged = false;

    // 检查缩放级别是否变化
    const zoomChanged = cache.lastZoomLevel !== undefined &&
                       Math.abs((cache.lastZoomLevel || 1.0) - currentZoom) > 0.01;

    // 检查可见时间范围是否变化
    const currentVisibleStartTime = visibleStartTime?.value || 0;
    const currentVisibleEndTime = visibleEndTime?.value || audioData.value.duration_ms;
    const timeRangeChanged =
      cache.lastVisibleStartTime !== currentVisibleStartTime ||
      cache.lastVisibleEndTime !== currentVisibleEndTime;

    return dataChanged || sizeChanged || offsetChanged || zoomChanged || timeRangeChanged;
  };

  /**
   * 创建音频波形路径
   * 将可见时间范围内的音频数据拉伸铺满整个画布宽度
   */
  const createAudioWaveformPath = (data: AudioAmplitudeData): Path2D => {
    const path = new Path2D();
    const samples = data.samples;
    const sampleCount = samples.length;

    if (sampleCount === 0) return path;

    const durationMs = data.duration_ms;
    const graphWidth = getGraphAreaWidth();
    const graphHeight = getGraphAreaHeight();

    // 获取可见时间范围
    const startTime = visibleStartTime?.value || 0;
    const endTime = visibleEndTime?.value || durationMs;



    // 确保时间范围有效
    const validStartTime = Math.max(0, Math.min(startTime, durationMs));
    const validEndTime = Math.max(validStartTime, Math.min(endTime, durationMs));

    // 如果时间范围无效，返回空路径
    if (validStartTime >= validEndTime) return path;

    // 计算每个采样点对应的时间
    const timePerSample = durationMs / sampleCount;

    // 计算可见时间范围对应的采样点索引范围
    const startIndex = Math.floor(validStartTime / timePerSample);
    const endIndex = Math.min(sampleCount - 1, Math.ceil(validEndTime / timePerSample));

    // 提取可见范围内的采样点
    const visibleSamples = samples.slice(startIndex, endIndex + 1);
    const visibleSampleCount = visibleSamples.length;

    // 如果没有可见采样点，返回空路径
    if (visibleSampleCount === 0) return path;

    // 找到最大振幅用于归一化
    const maxAmp = Math.max(Math.abs(data.max_amplitude), Math.abs(data.min_amplitude));

    // 使用优化的振幅缩放算法
    const scaleConfig: AmplitudeScaleConfig = {
      amplitudeScale,
      amplitudeBoost,
      maxHeightUsage: 0.9,
      enableAdaptiveScaling: true,
    };

    const finalAmplitudeScale = calculateOptimizedAmplitudeScale(maxAmp, graphHeight, scaleConfig);

    // 设置无声阈值
    const silenceThreshold = enableSilenceFiltering ? maxAmp * (silenceThresholdPercent / 100) : 0;

    // X轴位置（零基线）- 与Event波形保持一致的垂直偏移
    const centerY = mapIntensityToYLocal(0) - EVENT_WAVEFORM_VERTICAL_OFFSET;

    let isFirstPoint = true;
    const pathPoints: Array<{ x: number; y: number }> = [];

    // 遍历可见范围内的采样点，将其拉伸铺满整个画布宽度
    for (let i = 0; i < visibleSampleCount; i++) {
      const amplitude = visibleSamples[i];
      const absAmplitude = Math.abs(amplitude);

      // 将采样点索引映射到画布宽度：铺满整个宽度
      const x = visibleSampleCount > 1
        ? (i / (visibleSampleCount - 1)) * graphWidth
        : graphWidth / 2; // 单点时居中显示

      // 计算Y坐标
      let y: number;
      if (absAmplitude < silenceThreshold) {
        // 无声区域：直接绘制到X轴
        y = centerY;
      } else {
        // 有声区域：使用优化的振幅缩放算法
        const normalizedAmplitude = absAmplitude * finalAmplitudeScale;
        y = centerY - normalizedAmplitude;

        // 确保Y坐标不超出画布范围
        const minY = mapIntensityToYLocal(100);
        y = Math.max(y, minY);
      }

      pathPoints.push({ x, y });

      // 绘制路径
      if (isFirstPoint) {
        path.moveTo(x, y);
        isFirstPoint = false;
      } else {
        path.lineTo(x, y);
      }
    }

    // 闭合路径到X轴，形成填充区域
    if (pathPoints.length > 0) {
      // 从最后一个点连接到X轴
      const lastPoint = pathPoints[pathPoints.length - 1];
      path.lineTo(lastPoint.x, centerY);

      // 连接到第一个点的X轴位置
      const firstPoint = pathPoints[0];
      path.lineTo(firstPoint.x, centerY);

      path.closePath();
    }

    return path;
  };

  /**
   * 绘制音频波形
   */
  const drawAudioWaveform = (ctx: CanvasRenderingContext2D): void => {
    if (!hasAudioData.value || !audioData.value) return;

    // 检查是否需要重新生成波形路径
    if (shouldRegenerateWaveform()) {
      const path = createAudioWaveformPath(audioData.value);
      const currentZoom = currentZoomLevel?.value || 1.0;
      waveformCache.value = {
        data: audioData.value,
        renderedPath: path,
        lastCanvasWidth: getGraphAreaWidth(),
        lastScrollOffset: virtualScrollOffset.value,
        lastZoomLevel: currentZoom,
        lastVisibleStartTime: visibleStartTime?.value || 0,
        lastVisibleEndTime: visibleEndTime?.value || audioData.value.duration_ms,
      };
    }

    if (!waveformCache.value) return;

    // 设置绘制样式
    ctx.save();
    ctx.fillStyle = "rgba(233, 189, 231, 0.44)"; // 半透明填充
    ctx.lineCap = "round";
    ctx.lineJoin = "round";

    // 绘制填充区域
    ctx.fill(waveformCache.value.renderedPath);

    // 根据配置决定是否绘制边框
    if (showWaveformBorder) {
      ctx.strokeStyle = "#4A90E2"; // 蓝色音频波形边框
      ctx.lineWidth = 1;
      ctx.stroke(waveformCache.value.renderedPath);
    }

    ctx.restore();
  };

  /**
   * 清理缓存
   */
  const clearCache = () => {
    waveformCache.value = null;
  };

  return {
    // 状态
    audioData: readonly(audioData),
    hasAudioData,
    audioDurationMs,
    audioSampleRate,

    // 方法
    drawAudioWaveform,
    clearCache,
    getAmplitudeConfig,
  };
}
